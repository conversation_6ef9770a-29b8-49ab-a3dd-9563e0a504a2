#!/usr/bin/env python3
"""
LAS file processing utilities for BeautyMap
Supports reading LAS files, splitting by time, and saving results
"""

import numpy as np
import laspy
import os
from typing import List, Tuple, Optional, Dict
import pandas as pd
from tqdm import tqdm
import tempfile
import shutil


class LASProcessor:
    """Class for processing LAS files with BeautyMap"""
    
    def __init__(self, las_file_path: str):
        """
        Initialize LAS processor
        
        Args:
            las_file_path: Path to input LAS file
        """
        self.las_file_path = las_file_path
        self.las_data = None
        self.point_data = None
        self.time_stamps = None
        self.frames = []
        self.temp_dir = None
        
    def load_las_file(self) -> bool:
        """
        Load LAS file and extract point data
        
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            print(f"Loading LAS file: {self.las_file_path}")
            self.las_data = laspy.read(self.las_file_path)
            
            # Extract coordinates
            x = self.las_data.x
            y = self.las_data.y  
            z = self.las_data.z
            
            # Extract intensity if available
            intensity = None
            if hasattr(self.las_data, 'intensity'):
                intensity = self.las_data.intensity
                self.point_data = np.column_stack((x, y, z, intensity))
            else:
                self.point_data = np.column_stack((x, y, z))
            
            # Extract GPS time if available
            if hasattr(self.las_data, 'gps_time'):
                self.time_stamps = self.las_data.gps_time
                print(f"Found GPS time data: {len(self.time_stamps)} points")
            else:
                print("Warning: No GPS time found in LAS file. Using sequential numbering.")
                self.time_stamps = np.arange(len(x)) * 0.1  # Assume 10Hz
            
            print(f"Loaded {len(x)} points from LAS file")
            print(f"Point data shape: {self.point_data.shape}")
            print(f"Time range: {self.time_stamps.min():.3f} - {self.time_stamps.max():.3f} seconds")
            
            return True
            
        except Exception as e:
            print(f"Error loading LAS file: {e}")
            return False
    
    def split_by_time(self, frame_duration: float = 0.1) -> List[np.ndarray]:
        """
        Split point cloud into frames based on time stamps
        
        Args:
            frame_duration: Duration of each frame in seconds (default: 0.1s)
            
        Returns:
            List of point arrays for each frame
        """
        if self.point_data is None or self.time_stamps is None:
            print("Error: LAS data not loaded")
            return []
        
        print(f"Splitting point cloud into frames with {frame_duration}s duration...")
        
        # Calculate time bins
        min_time = self.time_stamps.min()
        max_time = self.time_stamps.max()
        time_bins = np.arange(min_time, max_time + frame_duration, frame_duration)
        
        # Assign points to frames
        frame_indices = np.digitize(self.time_stamps, time_bins) - 1
        frame_indices = np.clip(frame_indices, 0, len(time_bins) - 2)
        
        frames = []
        frame_info = []
        
        for i in tqdm(range(len(time_bins) - 1), desc="Creating frames"):
            mask = frame_indices == i
            frame_points = self.point_data[mask]
            
            if len(frame_points) > 0:
                frames.append(frame_points)
                frame_info.append({
                    'frame_id': i,
                    'start_time': time_bins[i],
                    'end_time': time_bins[i + 1],
                    'point_count': len(frame_points)
                })
        
        self.frames = frames
        
        print(f"Created {len(frames)} frames")
        print(f"Average points per frame: {np.mean([len(f) for f in frames]):.1f}")
        
        return frames
    
    def create_temp_pcd_files(self) -> str:
        """
        Create temporary PCD files from frames
        
        Returns:
            str: Path to temporary directory containing PCD files
        """
        if not self.frames:
            print("Error: No frames available. Run split_by_time() first.")
            return ""
        
        # Create temporary directory
        self.temp_dir = tempfile.mkdtemp(prefix="beautymap_las_")
        pcd_dir = os.path.join(self.temp_dir, "pcd")
        os.makedirs(pcd_dir, exist_ok=True)
        
        print(f"Creating temporary PCD files in: {pcd_dir}")
        
        # Import save_pcd function
        from utils.pcdpy3 import save_pcd
        
        # Save each frame as PCD
        for i, frame_points in tqdm(enumerate(self.frames), total=len(self.frames), desc="Saving PCD files"):
            pcd_path = os.path.join(pcd_dir, f"{i:06d}.pcd")
            save_pcd(pcd_path, frame_points)
        
        # Create a map file (use all points)
        map_path = os.path.join(self.temp_dir, "gt_cloud.pcd")
        save_pcd(map_path, self.point_data)
        
        print(f"Created {len(self.frames)} PCD files and map file")
        return self.temp_dir
    
    def save_result_as_las(self, result_points: np.ndarray, output_path: str) -> bool:
        """
        Save processed points as LAS file
        
        Args:
            result_points: Processed point cloud data
            output_path: Output LAS file path
            
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            print(f"Saving result as LAS file: {output_path}")
            
            # Create new LAS file
            header = laspy.LasHeader(point_format=2, version="1.2")
            header.add_extra_dim(laspy.ExtraBytesParams(name="processed", type=np.uint8))
            
            # Create LAS data
            las_out = laspy.LasData(header)
            
            # Set coordinates
            las_out.x = result_points[:, 0]
            las_out.y = result_points[:, 1]
            las_out.z = result_points[:, 2]
            
            # Set intensity if available
            if result_points.shape[1] >= 4:
                las_out.intensity = result_points[:, 3].astype(np.uint16)
            
            # Set other attributes from original file if available
            if self.las_data is not None:
                # Try to preserve original attributes for points that remain
                original_points = self.point_data[:, :3]
                result_coords = result_points[:, :3]
                
                # Find matching points (this is approximate)
                # In practice, you might want to use a more sophisticated matching algorithm
                if hasattr(self.las_data, 'classification'):
                    las_out.classification = np.zeros(len(result_points), dtype=np.uint8)
                
                if hasattr(self.las_data, 'return_number'):
                    las_out.return_number = np.ones(len(result_points), dtype=np.uint8)
                
                if hasattr(self.las_data, 'number_of_returns'):
                    las_out.number_of_returns = np.ones(len(result_points), dtype=np.uint8)
            
            # Write file
            las_out.write(output_path)
            
            print(f"Successfully saved {len(result_points)} points to {output_path}")
            return True
            
        except Exception as e:
            print(f"Error saving LAS file: {e}")
            return False
    
    def cleanup_temp_files(self):
        """Clean up temporary files"""
        if self.temp_dir and os.path.exists(self.temp_dir):
            print(f"Cleaning up temporary files: {self.temp_dir}")
            shutil.rmtree(self.temp_dir)
            self.temp_dir = None
    
    def get_frame_info(self) -> List[Dict]:
        """Get information about created frames"""
        if not self.frames:
            return []
        
        info = []
        for i, frame in enumerate(self.frames):
            info.append({
                'frame_id': i,
                'point_count': len(frame),
                'bounds': {
                    'x_min': frame[:, 0].min(),
                    'x_max': frame[:, 0].max(),
                    'y_min': frame[:, 1].min(),
                    'y_max': frame[:, 1].max(),
                    'z_min': frame[:, 2].min(),
                    'z_max': frame[:, 2].max()
                }
            })
        
        return info


def load_las_as_pcd_format(las_file_path: str) -> Tuple[np.ndarray, np.ndarray]:
    """
    Load LAS file and return in PCD-compatible format
    
    Args:
        las_file_path: Path to LAS file
        
    Returns:
        Tuple of (point_data, viewpoint) where:
        - point_data: numpy array of shape (N, 3) or (N, 4) with x,y,z,(intensity)
        - viewpoint: numpy array of shape (7,) with [x,y,z,qw,qx,qy,qz]
    """
    try:
        las_data = laspy.read(las_file_path)
        
        # Extract coordinates
        x = las_data.x
        y = las_data.y
        z = las_data.z
        
        # Extract intensity if available
        if hasattr(las_data, 'intensity'):
            intensity = las_data.intensity
            point_data = np.column_stack((x, y, z, intensity))
        else:
            point_data = np.column_stack((x, y, z))
        
        # Create default viewpoint (origin with no rotation)
        viewpoint = np.array([0.0, 0.0, 0.0, 1.0, 0.0, 0.0, 0.0])
        
        return point_data, viewpoint
        
    except Exception as e:
        print(f"Error loading LAS file: {e}")
        return None, None


def save_pcd_as_las(point_data: np.ndarray, output_path: str, 
                   original_las_path: Optional[str] = None) -> bool:
    """
    Save point data as LAS file
    
    Args:
        point_data: Point cloud data array
        output_path: Output LAS file path
        original_las_path: Optional path to original LAS file for header info
        
    Returns:
        bool: True if successful, False otherwise
    """
    try:
        # Create LAS header
        header = laspy.LasHeader(point_format=2, version="1.2")
        
        # Create LAS data
        las_out = laspy.LasData(header)
        
        # Set coordinates
        las_out.x = point_data[:, 0]
        las_out.y = point_data[:, 1]
        las_out.z = point_data[:, 2]
        
        # Set intensity if available
        if point_data.shape[1] >= 4:
            las_out.intensity = point_data[:, 3].astype(np.uint16)
        
        # Set default values for required fields
        las_out.classification = np.zeros(len(point_data), dtype=np.uint8)
        las_out.return_number = np.ones(len(point_data), dtype=np.uint8)
        las_out.number_of_returns = np.ones(len(point_data), dtype=np.uint8)
        
        # Write file
        las_out.write(output_path)
        
        return True
        
    except Exception as e:
        print(f"Error saving LAS file: {e}")
        return False
