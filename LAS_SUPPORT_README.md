# BeautyMap LAS文件支持

## 概述

BeautyMap现在支持LAS/LAZ文件的直接处理！您可以输入一个LAS文件，程序会自动按照时间戳将其分割成0.1秒一帧的点云数据，处理完成后再将结果保存为LAS文件。

## 新增功能

### 🎯 **核心功能**
- **LAS文件读取**：支持LAS/LAZ格式的点云文件
- **时间分帧**：根据GPS时间戳自动分割点云为时序帧
- **动态点去除**：使用BeautyMap算法去除动态点
- **LAS文件输出**：将处理结果保存为LAS格式

### 📊 **支持的数据格式**
- **输入**：LAS/LAZ文件（包含GPS时间戳）
- **输出**：LAS文件（静态点云）
- **临时文件**：PCD格式（可选保留）

## 安装依赖

```bash
pip install laspy pandas
```

## 使用方法

### 1. 简单处理
```bash
python process_las.py input.las
```
- 输入：`input.las`
- 输出：`input_beautymap.las`
- 帧时长：0.1秒（默认）

### 2. 指定输出文件
```bash
python process_las.py input.las output.las
```

### 3. 自定义参数
```bash
python process_las.py input.las \
    --frame_duration 0.05 \
    --dis_range 15 \
    --xy_resolution 0.3 \
    --h_res 0.1
```

### 4. 使用主程序
```bash
python main_las.py las \
    --las_file_path input.las \
    --output_las_path output.las \
    --frame_duration 0.1
```

## 参数说明

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `--frame_duration` | float | 0.1 | 每帧时长（秒） |
| `--dis_range` | float | 10 | 处理范围（米） |
| `--xy_resolution` | float | 0.5 | XY网格分辨率（米） |
| `--h_res` | float | 0.2 | Z网格分辨率（米） |
| `--run_frame_num` | int | -1 | 处理帧数（-1=全部） |
| `--keep_temp_files` | bool | False | 保留临时文件 |

## 处理流程

```mermaid
graph TD
    A[LAS文件输入] --> B[读取点云数据]
    B --> C[提取GPS时间戳]
    C --> D[按时间分帧 0.1s/帧]
    D --> E[生成临时PCD文件]
    E --> F[BeautyMap算法处理]
    F --> G[去除动态点]
    G --> H[保存为LAS文件]
    H --> I[清理临时文件]
```

## 示例

### 处理激光雷达数据
```bash
# 处理车载激光雷达数据
python process_las.py vehicle_lidar.las \
    --frame_duration 0.1 \
    --dis_range 50 \
    --xy_resolution 0.5

# 处理无人机激光雷达数据  
python process_las.py drone_lidar.las \
    --frame_duration 0.05 \
    --dis_range 20 \
    --xy_resolution 0.2
```

### 测试功能
```bash
# 运行LAS支持测试
python test_las_support.py

# 处理少量帧进行测试
python process_las.py input.las --run_frame_num 10
```

## 技术细节

### 时间分帧算法
1. **读取GPS时间**：从LAS文件中提取GPS时间戳
2. **计算时间区间**：根据`frame_duration`参数划分时间窗口
3. **点云分组**：将点云按时间戳分配到对应帧中
4. **生成PCD文件**：每帧保存为独立的PCD文件

### 数据保持
- **坐标精度**：保持原始LAS文件的坐标精度
- **强度信息**：保留点云强度数据
- **分类信息**：尽可能保持原始分类信息
- **时间信息**：保留GPS时间戳

### 内存优化
- **流式处理**：逐帧处理避免内存溢出
- **临时文件**：使用临时PCD文件减少内存占用
- **自动清理**：处理完成后自动删除临时文件

## 性能建议

### 参数优化
```bash
# 高精度处理（慢但精确）
--frame_duration 0.05 --xy_resolution 0.1 --h_res 0.05

# 平衡处理（推荐）
--frame_duration 0.1 --xy_resolution 0.5 --h_res 0.2

# 快速处理（快但粗糙）
--frame_duration 0.2 --xy_resolution 1.0 --h_res 0.5
```

### 大文件处理
```bash
# 分批处理大文件
python process_las.py large_file.las --run_frame_num 100
python process_las.py large_file.las --run_frame_num 200 --skip_frames 100
```

## 故障排除

### 常见问题

1. **没有GPS时间戳**
   ```
   Warning: No GPS time found in LAS file. Using sequential numbering.
   ```
   - 解决：程序会自动生成时间戳（假设10Hz频率）

2. **内存不足**
   ```
   MemoryError: Unable to allocate array
   ```
   - 解决：减小`dis_range`或使用`--run_frame_num`分批处理

3. **文件格式错误**
   ```
   Error loading LAS file: Invalid file format
   ```
   - 解决：确保文件是有效的LAS/LAZ格式

### 调试模式
```bash
# 保留临时文件用于调试
python process_las.py input.las --keep_temp_files

# 处理少量帧进行调试
python process_las.py input.las --run_frame_num 5
```

## 输出示例

```
BeautyMap LAS File Processor
============================================================
Input file: vehicle_scan.las
Output file: vehicle_scan_beautymap.las
Options:
  frame_duration: 0.1
  dis_range: 10
============================================================

Loading LAS file: vehicle_scan.las
Found GPS time data: 1000000 points
Loaded 1000000 points from LAS file
Point data shape: (1000000, 4)
Time range: 0.000 - 100.000 seconds

Splitting point cloud into frames with 0.1s duration...
Created 1000 frames
Average points per frame: 1000.0

Creating temporary PCD files...
Created 1000 PCD files and map file

Processing with BeautyMap algorithm...
Processing 1000 frames...
Found 150000 dynamic points to remove

Saving result as LAS file...
Successfully saved 850000 points

✅ Processing completed successfully!
Input points: 1000000
Output points: 850000
Points removed: 150000
Reduction: 15.0%
```

## 版本信息

- **BeautyMap版本**：支持LAS的增强版
- **依赖库**：laspy 2.5.4+, pandas 2.3.0+
- **支持格式**：LAS 1.2+, LAZ（压缩格式）
- **Python版本**：3.8+
