#!/usr/bin/env python3
"""
Quick fix for "No query points in map range" issue
Provides immediate solutions and parameter recommendations
"""

import numpy as np
import os
import sys


def quick_analysis(data_dir: str):
    """Quick analysis to determine the issue and provide solutions"""
    
    print("🔧 BeautyMap Frame Skip Issue - Quick Fix")
    print("=" * 50)
    
    # Check if it's a LAS processing issue
    if "pcd" not in os.listdir(data_dir):
        print("❌ No 'pcd' directory found!")
        print("💡 If you're processing LAS files, make sure to use:")
        print("   python main_las.py las --las_file_path your_file.las")
        return
    
    # Load a few sample frames to analyze
    pcd_dir = os.path.join(data_dir, "pcd")
    pcd_files = sorted([f for f in os.listdir(pcd_dir) if f.endswith('.pcd')])
    
    if len(pcd_files) == 0:
        print("❌ No PCD files found in pcd directory!")
        return
    
    print(f"📁 Found {len(pcd_files)} PCD files")
    
    # Quick analysis of first, middle, and last frames
    sample_indices = [0, len(pcd_files)//2, len(pcd_files)-1]
    sample_files = [pcd_files[i] for i in sample_indices if i < len(pcd_files)]
    
    print(f"🔍 Analyzing sample frames: {[f for f in sample_files]}")
    
    try:
        from lib.bee_tree import BEETree
        
        # Load map
        raw_map_path = os.path.join(data_dir, "raw_map.pcd")
        if not os.path.exists(raw_map_path):
            raw_map_path = os.path.join(data_dir, "gt_cloud.pcd")
        
        if not os.path.exists(raw_map_path):
            print("❌ No map file found!")
            return
        
        print(f"📍 Loading map: {os.path.basename(raw_map_path)}")
        
        map_tree = BEETree()
        map_tree.set_points_from_file(raw_map_path)
        
        map_bounds = {
            'x': [map_tree.original_points[:, 0].min(), map_tree.original_points[:, 0].max()],
            'y': [map_tree.original_points[:, 1].min(), map_tree.original_points[:, 1].max()],
            'z': [map_tree.original_points[:, 2].min(), map_tree.original_points[:, 2].max()],
        }
        
        map_size = max(
            map_bounds['x'][1] - map_bounds['x'][0],
            map_bounds['y'][1] - map_bounds['y'][0]
        )
        
        print(f"   Map size: {map_size:.1f}m")
        print(f"   Map bounds: X[{map_bounds['x'][0]:.1f}, {map_bounds['x'][1]:.1f}], "
              f"Y[{map_bounds['y'][0]:.1f}, {map_bounds['y'][1]:.1f}]")
        
        # Analyze sample frames
        max_sensor_distance = 0
        max_point_range = 0
        
        for pcd_file in sample_files:
            frame_path = os.path.join(pcd_dir, pcd_file)
            
            frame_tree = BEETree()
            frame_tree.set_points_from_file(frame_path)
            
            sensor_pos = frame_tree.sensor_origin_pose[:3]
            sensor_distance = np.sqrt(sensor_pos[0]**2 + sensor_pos[1]**2)
            
            # Calculate max distance from sensor to points
            distances = np.sqrt(
                (frame_tree.original_points[:, 0] - sensor_pos[0])**2 + 
                (frame_tree.original_points[:, 1] - sensor_pos[1])**2
            )
            point_range = distances.max()
            
            max_sensor_distance = max(max_sensor_distance, sensor_distance)
            max_point_range = max(max_point_range, point_range)
            
            print(f"   {pcd_file}: sensor distance {sensor_distance:.1f}m, point range {point_range:.1f}m")
        
        # Calculate recommended parameters
        required_range = max(max_sensor_distance + max_point_range, map_size * 0.6) * 1.5
        recommended_dis_range = np.ceil(required_range / 5) * 5  # Round up to nearest 5
        
        # Determine resolution based on range
        if recommended_dis_range <= 20:
            recommended_xy_res = 0.2
            recommended_h_res = 0.1
        elif recommended_dis_range <= 50:
            recommended_xy_res = 0.5
            recommended_h_res = 0.2
        else:
            recommended_xy_res = 1.0
            recommended_h_res = 0.5
        
        print(f"\n🎯 RECOMMENDED SOLUTION:")
        print(f"   Current issue: Processing range too small")
        print(f"   Required range: {required_range:.1f}m")
        print(f"   Recommended dis_range: {recommended_dis_range:.0f}m")
        
        print(f"\n📋 Use these parameters:")
        print(f"   --dis_range {recommended_dis_range:.0f}")
        print(f"   --xy_resolution {recommended_xy_res}")
        print(f"   --h_res {recommended_h_res}")
        
        print(f"\n🚀 Complete command:")
        if "las" in data_dir.lower() or any("las" in f.lower() for f in os.listdir(data_dir) if f.endswith(('.las', '.laz'))):
            print(f"   python main_las.py las --las_file_path your_file.las \\")
            print(f"       --dis_range {recommended_dis_range:.0f} \\")
            print(f"       --xy_resolution {recommended_xy_res} \\")
            print(f"       --h_res {recommended_h_res}")
        else:
            print(f"   python main.py --data_dir {data_dir} \\")
            print(f"       --dis_range {recommended_dis_range:.0f} \\")
            print(f"       --xy_resolution {recommended_xy_res} \\")
            print(f"       --h_res {recommended_h_res}")
        
        # Additional suggestions
        print(f"\n💡 Additional suggestions:")
        print(f"   • If still having issues, try increasing dis_range to {recommended_dis_range * 1.5:.0f}")
        print(f"   • For faster processing, use xy_resolution {recommended_xy_res * 2}")
        print(f"   • For higher accuracy, use xy_resolution {recommended_xy_res / 2}")
        
        # Memory warning
        matrix_size = int(recommended_dis_range * 2 / recommended_xy_res)
        memory_estimate = matrix_size * matrix_size * 8 / 1024 / 1024  # MB
        
        if memory_estimate > 1000:  # > 1GB
            print(f"\n⚠️  Memory warning:")
            print(f"   Estimated memory usage: {memory_estimate:.0f}MB")
            print(f"   Consider using larger xy_resolution if memory is limited")
        
    except Exception as e:
        print(f"❌ Error during analysis: {e}")
        print(f"\n🔧 Manual solution:")
        print(f"   Try increasing the dis_range parameter:")
        print(f"   python main.py --data_dir {data_dir} --dis_range 50")


def create_test_script(data_dir: str, dis_range: float, xy_resolution: float, h_res: float):
    """Create a test script with recommended parameters"""
    
    script_content = f'''#!/usr/bin/env python3
"""
Auto-generated test script for BeautyMap with optimized parameters
Generated by fix_frame_skip_issue.py
"""

import os
import sys

# Add current directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def main():
    """Run BeautyMap with optimized parameters"""
    
    data_dir = r"{data_dir}"
    
    # Check if this is LAS processing
    if any(f.endswith(('.las', '.laz')) for f in os.listdir(data_dir) if os.path.isfile(os.path.join(data_dir, f))):
        print("Detected LAS files - using LAS processing mode")
        from main_las import main_las
        
        las_files = [f for f in os.listdir(data_dir) if f.endswith(('.las', '.laz'))]
        if las_files:
            las_file = os.path.join(data_dir, las_files[0])
            main_las(
                las_file_path=las_file,
                dis_range={dis_range},
                xy_resolution={xy_resolution},
                h_res={h_res},
                run_frame_num=10  # Test with 10 frames first
            )
        else:
            print("No LAS files found!")
    else:
        print("Using PCD processing mode")
        from main import main
        
        main(
            data_dir=data_dir,
            dis_range={dis_range},
            xy_resolution={xy_resolution},
            h_res={h_res},
            run_file_num=10  # Test with 10 frames first
        )

if __name__ == "__main__":
    main()
'''
    
    script_path = "test_optimized_parameters.py"
    with open(script_path, 'w') as f:
        f.write(script_content)
    
    print(f"\n📝 Created test script: {script_path}")
    print(f"   Run with: python {script_path}")


def main():
    """Main function"""
    
    if len(sys.argv) < 2:
        print("Usage: python fix_frame_skip_issue.py <data_dir>")
        print("\nExample:")
        print("  python fix_frame_skip_issue.py G:/Data/SLAM/test_dynamic/semindoor")
        return
    
    data_dir = sys.argv[1]
    
    if not os.path.exists(data_dir):
        print(f"❌ Data directory not found: {data_dir}")
        return
    
    quick_analysis(data_dir)


if __name__ == "__main__":
    main()
