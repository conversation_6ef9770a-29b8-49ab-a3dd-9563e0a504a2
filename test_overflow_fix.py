#!/usr/bin/env python3
"""
Test script to verify the overflow fix for BeautyMap
"""

import numpy as np
import sys
import os

# Add the current directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_large_integers():
    """Test that large integer operations work correctly"""
    print("Testing large integer operations...")
    
    MAX_HEIGHT = 32
    
    # Test 1: Basic bit shift operations
    try:
        result1 = np.int64(1) << MAX_HEIGHT
        print(f"✓ np.int64(1) << {MAX_HEIGHT} = {result1}")
    except OverflowError as e:
        print(f"✗ Overflow error in bit shift: {e}")
        return False
    
    # Test 2: Large mask creation
    try:
        max_mask = (np.int64(1) << MAX_HEIGHT) - 1
        print(f"✓ Max mask creation successful: {max_mask}")
    except OverflowError as e:
        print(f"✗ Overflow error in mask creation: {e}")
        return False
    
    # Test 3: Array operations with int64
    try:
        test_array = np.zeros((10, 10), dtype=np.int64)
        test_array[0, 0] = max_mask
        print(f"✓ Array operations with int64 successful")
    except OverflowError as e:
        print(f"✗ Overflow error in array operations: {e}")
        return False
    
    # Test 4: Binary operations
    try:
        a = np.int64(1) << 31
        b = np.int64(1) << 30
        result = a | b
        print(f"✓ Binary OR operations successful: {result}")
    except OverflowError as e:
        print(f"✗ Overflow error in binary operations: {e}")
        return False
    
    return True

def test_bee_tree_import():
    """Test that BEETree can be imported without errors"""
    print("\nTesting BEETree import...")
    
    try:
        from lib.bee_tree import BEETree, MAX_HEIGHT
        print(f"✓ BEETree imported successfully, MAX_HEIGHT = {MAX_HEIGHT}")
        
        # Test basic instantiation
        tree = BEETree()
        print("✓ BEETree instantiation successful")
        
        return True
    except Exception as e:
        print(f"✗ Error importing or instantiating BEETree: {e}")
        return False

def main():
    """Run all tests"""
    print("BeautyMap Overflow Fix Test")
    print("=" * 40)
    
    success = True
    
    # Test large integer operations
    if not test_large_integers():
        success = False
    
    # Test BEETree import
    if not test_bee_tree_import():
        success = False
    
    print("\n" + "=" * 40)
    if success:
        print("✓ All tests passed! The overflow fix appears to be working.")
    else:
        print("✗ Some tests failed. Please check the error messages above.")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
