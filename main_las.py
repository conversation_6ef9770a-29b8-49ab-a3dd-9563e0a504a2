#!/usr/bin/env python3
"""
BeautyMap main program with LAS file support
Supports processing LAS files by splitting them into time-based frames
"""

import numpy as np
np.seterr(divide='ignore', invalid='ignore')
np.set_printoptions(threshold=np.inf)

from tqdm import tqdm
import os, fire
import dztimer

# self
from lib.bee_tree import BEETree
from utils.pcdpy3 import save_pcd
from utils.laspy_utils import LASProcessor, save_pcd_as_las


def main_las(
    las_file_path: str,
    output_las_path: str = None,
    frame_duration: float = 0.1,
    dis_range: float = 10,
    xy_resolution: float = 0.5,
    h_res: float = 0.2,
    run_frame_num: int = -1,  # -1 for all frames
    keep_temp_files: bool = False
):
    """
    Process LAS file with BeautyMap algorithm
    
    Args:
        las_file_path: Path to input LAS file
        output_las_path: Path for output LAS file (optional)
        frame_duration: Duration of each frame in seconds (default: 0.1s)
        dis_range: Processing range in meters
        xy_resolution: XY grid resolution in meters
        h_res: Z grid resolution in meters
        run_frame_num: Number of frames to process (-1 for all)
        keep_temp_files: Whether to keep temporary PCD files
    """
    
    timer = dztimer.Timing()
    timer.start("Total")
    
    # Validate input file
    if not os.path.exists(las_file_path):
        print(f"Error: LAS file not found: {las_file_path}")
        return
    
    # Set default output path
    if output_las_path is None:
        base_name = os.path.splitext(las_file_path)[0]
        output_las_path = f"{base_name}_beautymap_output.las"
    
    print(f"Processing LAS file: \033[1m{las_file_path}\033[0m")
    print(f"Output will be saved to: \033[1m{output_las_path}\033[0m")
    print(f"Frame duration: {frame_duration}s")
    
    # Initialize LAS processor
    las_processor = LASProcessor(las_file_path)
    
    try:
        # Step 1: Load LAS file
        timer[0].start("LAS Loading")
        if not las_processor.load_las_file():
            print("Failed to load LAS file")
            return
        timer[0].stop()
        
        # Step 2: Split into frames
        timer[1].start("Frame Splitting")
        frames = las_processor.split_by_time(frame_duration)
        if not frames:
            print("Failed to create frames from LAS file")
            return
        timer[1].stop()
        
        # Step 3: Create temporary PCD files
        timer[2].start("PCD Creation")
        temp_dir = las_processor.create_temp_pcd_files()
        if not temp_dir:
            print("Failed to create temporary PCD files")
            return
        timer[2].stop()
        
        print(f"Created temporary files in: {temp_dir}")
        
        # Step 4: Process with BeautyMap algorithm
        timer[3].start("BeautyMap Processing")
        result_points = process_with_beautymap(
            temp_dir, dis_range, xy_resolution, h_res, run_frame_num
        )
        timer[3].stop()
        
        if result_points is None:
            print("BeautyMap processing failed")
            return
        
        # Step 5: Save result as LAS
        timer[4].start("LAS Saving")
        success = las_processor.save_result_as_las(result_points, output_las_path)
        if not success:
            print("Failed to save result as LAS file")
            return
        timer[4].stop()
        
        print(f"\n✅ Processing completed successfully!")
        print(f"Input points: {len(las_processor.point_data)}")
        print(f"Output points: {len(result_points)}")
        print(f"Points removed: {len(las_processor.point_data) - len(result_points)}")
        print(f"Reduction: {(1 - len(result_points)/len(las_processor.point_data))*100:.1f}%")
        
    finally:
        # Cleanup temporary files
        if not keep_temp_files:
            las_processor.cleanup_temp_files()
        else:
            print(f"Temporary files kept at: {temp_dir}")
    
    # Show timing results
    timer.stop()
    timer.print(title="BeautyMap LAS Processing", random_colors=True, bold=True)
    print(f"{os.path.basename(__file__)}: All processing completed successfully!")


def process_with_beautymap(data_dir: str, dis_range: float, xy_resolution: float, 
                          h_res: float, run_frame_num: int = -1) -> np.ndarray:
    """
    Process point cloud data using BeautyMap algorithm
    
    Args:
        data_dir: Directory containing PCD files
        dis_range: Processing range
        xy_resolution: XY resolution
        h_res: Z resolution
        run_frame_num: Number of frames to process
        
    Returns:
        numpy.ndarray: Processed point cloud data
    """
    
    # Find map file
    raw_map_path = os.path.join(data_dir, "raw_map.pcd")
    if not os.path.exists(raw_map_path):
        raw_map_path = os.path.join(data_dir, "gt_cloud.pcd")
    
    if not os.path.exists(raw_map_path):
        print("No map file found in temporary directory")
        return None
    
    # Map generation
    print("Generating map...")
    Mpts = BEETree()
    Mpts.set_points_from_file(raw_map_path)
    Mpts.set_unit_params(xy_resolution, xy_resolution, h_res)
    Mpts.non_negatification_all_map_points()
    Mpts.calculate_matrix_order()
    Mpts.generate_map_binary_tree()
    Mpts.get_binary_matrix()
    Mpts.get_minz_matrix()
    
    # Initialize global triggers
    global_trigger = np.zeros([Mpts.matrix_order, Mpts.matrix_order], dtype=np.int64)
    global_ground_trigger = np.zeros([Mpts.max_height, Mpts.matrix_order, Mpts.matrix_order], dtype=np.int64)
    
    # Process frames
    pcd_dir = os.path.join(data_dir, "pcd")
    all_pcd_files = sorted([f for f in os.listdir(pcd_dir) if f.endswith('.pcd')])
    
    if run_frame_num > 0:
        all_pcd_files = all_pcd_files[:run_frame_num]
    
    print(f"Processing {len(all_pcd_files)} frames...")
    
    for file_cnt, pcd_file in tqdm(enumerate(all_pcd_files), total=len(all_pcd_files), 
                                  ncols=100, desc="Processing Frames"):
        
        # Query initialization
        Qpts = BEETree()
        Qpts.matrix_order = int(dis_range / xy_resolution)
        Qpts.set_points_from_file(os.path.join(pcd_dir, pcd_file))
        Qpts.set_unit_params(xy_resolution, xy_resolution, h_res)
        k = Qpts.transform_on_points(Mpts.coordinate_offset) * xy_resolution / h_res
        Qpts.calculate_query_matrix_start_id()
        
        # Get ROI matrices
        map_binary_matrix_roi = Qpts.calculate_map_roi(Mpts.binary_matrix)
        minz_matrix_roi = Qpts.calculate_map_roi(Mpts.minz_matrix)
        outlier_matrix_roi = Qpts.calculate_map_roi(Mpts.outlier_matrix)
        
        # Query generation
        is_valid_frame = Qpts.generate_query_binary_tree(minz_matrix_roi)
        if not is_valid_frame:
            print(f"⚠️  Skip frame {file_cnt}: {pcd_file}. No query points in map range!")

            # Provide diagnostic information
            points_in_map_frame = Qpts.non_negtive_points - [Qpts.start_xy[0], Qpts.start_xy[1], 0]
            sensor_distance = np.sqrt(Qpts.non_negtive_center[0]**2 + Qpts.non_negtive_center[1]**2)

            print(f"   📊 Frame info: {len(Qpts.original_points)} points, sensor distance: {sensor_distance:.1f}m")
            print(f"   📏 Point range: X[{points_in_map_frame[:, 0].min():.1f}, {points_in_map_frame[:, 0].max():.1f}], "
                  f"Y[{points_in_map_frame[:, 1].min():.1f}, {points_in_map_frame[:, 1].max():.1f}]")
            print(f"   🎯 Processing area: {Qpts.matrix_order * xy_resolution:.1f}m x {Qpts.matrix_order * xy_resolution:.1f}m")

            if sensor_distance > dis_range:
                print(f"   💡 Suggestion: Increase --dis_range to at least {sensor_distance * 1.2:.0f}")

            continue
        
        Qpts.get_binary_matrix()
        
        # Matrix comparison
        binary_xor = (~Qpts.binary_matrix) & map_binary_matrix_roi
        
        # Static restoration
        binary_xor &= ~Qpts.generate_static_restoration_mask(k, minz_matrix_roi, outlier_matrix_roi)
        binary_xor &= ~(map_binary_matrix_roi & -map_binary_matrix_roi)
        binary_xor &= ~Qpts.reverse_virtual_ray_casting(binary_xor, minz_matrix_roi)
        
        # Fine ground segmentation
        ground_index_matrix = np.log2((binary_xor & -binary_xor) >> 1).astype(int)
        ground_trigger = Mpts.calculate_ground_mask(Qpts, ground_index_matrix)
        
        # Update global triggers
        global_trigger[Qpts.start_id_x:Qpts.start_id_x+Qpts.matrix_order, 
                      Qpts.start_id_y:Qpts.start_id_y+Qpts.matrix_order] |= binary_xor
        global_ground_trigger[:, Qpts.start_id_x:Qpts.start_id_x+Qpts.matrix_order, 
                             Qpts.start_id_y:Qpts.start_id_y+Qpts.matrix_order] |= ground_trigger
    
    # Extract dynamic points
    points_index2Remove = []
    print("Extracting dynamic points...")
    
    for (i, j) in list(zip(*np.where(global_trigger != 0))):
        z = Mpts.binTo3id(global_trigger[i][j])
        for idz in z:
            if Mpts.root_matrix[i][j] is not None and Mpts.root_matrix[i][j].children[idz] is not None:
                points_index2Remove += (Mpts.root_matrix[i][j].children[idz].pts_id).tolist()
        
        for cid in range(len(global_ground_trigger)):
            gz = Mpts.binTo3id(global_ground_trigger[cid][i][j])
            for idgz in gz:
                if (Mpts.root_matrix[i][j] is not None and 
                    Mpts.root_matrix[i][j].children[cid] is not None and
                    Mpts.root_matrix[i][j].children[cid].children[idgz] is not None):
                    points_index2Remove += (Mpts.root_matrix[i][j].children[cid].children[idgz].pts_id).tolist()
    
    print(f"Found {len(points_index2Remove)} dynamic points to remove")
    
    # Get static points
    points_index2Retain = np.setdiff1d(np.arange(len(Mpts.original_points)), points_index2Remove)
    result_points = Mpts.original_points[points_index2Retain]
    
    return result_points


def main_pcd_directory(
    data_dir: str = "G:/Data/SLAM/test_dynamic/semindoor",
    dis_range: float = 10,
    xy_resolution: float = 0.5,
    h_res: float = 0.2,
    run_file_num: int = -1,  # -1 for all files
):
    """
    Original main function for processing PCD directory
    (Kept for backward compatibility)
    """
    
    timer = dztimer.Timing()
    timer.start("Total")
    print(f"We will process the data in folder: \033[1m {data_dir} \033[0m")

    # read raw map or gt cloud
    raw_map_path = f"{data_dir}/raw_map.pcd"
    if not os.path.exists(raw_map_path):
        # it's only for reading raw map no label will be used.
        raw_map_path = f"{data_dir}/gt_cloud.pcd"

    if not os.path.exists(raw_map_path):
        print("No raw map found, Please check your data_dir.")
        return

    # Process with BeautyMap
    result_points = process_with_beautymap(data_dir, dis_range, xy_resolution, h_res, run_file_num)
    
    if result_points is not None:
        # Save result
        save_pcd(f"{data_dir}/beautymap_output.pcd", result_points)
        print(f"Saved result to {data_dir}/beautymap_output.pcd")
    
    # show timer
    timer.stop()
    timer.print(title="BeautyMap", random_colors=True, bold=True)
    print(f"{os.path.basename(__file__)}: All codes run successfully, Close now..")


if __name__ == '__main__':
    fire.Fire({
        'las': main_las,
        'pcd': main_pcd_directory
    })
