# BeautyMap 使用指南

## 问题修复说明

### 已修复的 OverflowError 问题

原始代码中存在整数溢出问题，主要原因是：
1. 使用 `2 ** MAX_HEIGHT` (当 MAX_HEIGHT=32 时) 产生的大整数超出了C long类型范围
2. numpy数组使用 `dtype=int` 在32位系统上可能不够大
3. 位移操作 `1 << MAX_HEIGHT` 可能导致溢出

### 修复内容

1. **数据类型修复**：将所有涉及大整数的numpy数组改为使用 `dtype=np.int64`
2. **位运算优化**：使用 `np.int64(1) << n` 替代 `2 ** n` 避免溢出
3. **掩码计算优化**：重构了 `generate_static_restoration_mask` 函数中的大整数计算

## 环境配置

### 系统要求
- Python 3.8+
- Windows/Linux/MacOS
- 至少8GB内存（处理大型点云数据）

### 安装依赖
```bash
pip install -r requirements.txt
```

**新增LAS文件支持依赖**：
- `laspy`: LAS文件读写库
- `pandas`: 数据处理库

### 数据路径配置
您的数据路径已配置为：`G:/Data/SLAM/test_dynamic/semindoor`

确保数据目录结构如下：
```
G:/Data/SLAM/test_dynamic/semindoor/
├── pcd/                    # 包含所有.pcd文件的目录
│   ├── 000000.pcd
│   ├── 000001.pcd
│   └── ...
├── gt_cloud.pcd           # 或 raw_map.pcd (地图文件)
└── beautymap_output.pcd   # 输出文件（程序运行后生成）
```

## 运行程序

### PCD文件处理（原有功能）

#### 基本运行
```bash
python main.py
```

#### 自定义参数运行
```bash
python main.py --data_dir G:/Data/SLAM/test_dynamic/semindoor --dis_range 10 --xy_resolution 0.5 --h_res 0.2
```

#### 测试运行（处理前5帧）
```bash
python main.py --run_file_num 5
```

### LAS文件处理（新功能）

#### 简单LAS文件处理
```bash
python process_las.py input.las
```

#### 指定输出文件
```bash
python process_las.py input.las output.las
```

#### 自定义参数处理LAS文件
```bash
python process_las.py input.las --frame_duration 0.1 --dis_range 10 --xy_resolution 0.5 --h_res 0.2
```

#### 使用主程序处理LAS文件
```bash
python main_las.py las --las_file_path input.las --output_las_path output.las
```

### 参数说明

#### PCD文件处理参数
- `--data_dir`: 数据目录路径
- `--dis_range`: 处理范围（米），从中心点到正方形边界的距离
- `--xy_resolution`: XY平面网格分辨率（米）
- `--h_res`: Z轴（高度）网格分辨率（米）
- `--run_file_num`: 处理的文件数量（-1表示处理所有文件）

#### LAS文件处理参数
- `--las_file_path`: 输入LAS文件路径
- `--output_las_path`: 输出LAS文件路径（可选）
- `--frame_duration`: 每帧时长（秒），默认0.1秒
- `--dis_range`: 处理范围（米），默认10米
- `--xy_resolution`: XY平面网格分辨率（米），默认0.5米
- `--h_res`: Z轴网格分辨率（米），默认0.2米
- `--run_frame_num`: 处理的帧数量（-1表示处理所有帧）
- `--keep_temp_files`: 保留临时PCD文件

### 推荐参数设置

**Semi-indoor 数据**（您的数据类型）：
```bash
python main.py --dis_range 10 --xy_resolution 0.5 --h_res 0.2
```

**KITTI 数据**：
```bash
python main.py --dis_range 40 --xy_resolution 1.0 --h_res 0.5
```

## 输出结果

程序运行完成后会生成：
- `beautymap_output.pcd`: 去除动态点后的静态地图点云

## 性能优化建议

1. **内存使用**：处理大型数据集时，可以分批处理：
   ```bash
   python main.py --run_file_num 100  # 每次处理100帧
   ```

2. **参数调优**：
   - 减小 `dis_range` 可以减少内存使用和计算时间
   - 增大 `xy_resolution` 和 `h_res` 可以减少计算复杂度但可能降低精度

3. **监控进度**：程序会显示处理进度和各阶段耗时

## 故障排除

### 常见问题

1. **内存不足**：
   - 减小 `dis_range` 参数
   - 分批处理数据 (`--run_file_num`)

2. **数据路径错误**：
   - 确保数据路径正确
   - 检查是否存在 `gt_cloud.pcd` 或 `raw_map.pcd`

3. **依赖包问题**：
   ```bash
   python setup_environment.py  # 运行环境检查脚本
   ```

### 测试脚本

运行测试脚本验证修复：
```bash
python test_overflow_fix.py
```

## 技术细节

### 算法流程
1. **地图生成**：从地图文件构建BEE树结构
2. **查询处理**：逐帧处理点云数据
3. **矩阵比较**：识别动态点
4. **静态恢复**：处理遮挡和视线问题
5. **精细地面分割**：优化地面点处理

### 数据结构
- **BEETree**: 二进制编码消除树，用于高效的点云组织
- **Binary Matrix**: 二进制矩阵表示占用状态
- **Ground Mask**: 地面点掩码用于精确分割

## 联系支持

如果遇到问题，请检查：
1. Python版本是否为3.8+
2. 所有依赖是否正确安装
3. 数据路径和格式是否正确
4. 系统内存是否充足

程序已经过测试，可以正常处理您的semi-indoor数据集。
