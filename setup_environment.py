#!/usr/bin/env python3
"""
Environment setup script for BeautyMap
"""

import os
import sys
import subprocess

def check_python_version():
    """Check if Python version is compatible"""
    version = sys.version_info
    print(f"Python version: {version.major}.{version.minor}.{version.micro}")
    
    if version.major != 3 or version.minor < 8:
        print("❌ Python 3.8+ is required")
        return False
    else:
        print("✅ Python version is compatible")
        return True

def install_requirements():
    """Install required packages"""
    print("\nInstalling requirements...")
    
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
        print("✅ Requirements installed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to install requirements: {e}")
        return False

def check_data_path():
    """Check if data path exists"""
    data_path = "G:/Data/SLAM/test_dynamic/semindoor/pcd"
    print(f"\nChecking data path: {data_path}")
    
    if os.path.exists(data_path):
        pcd_files = [f for f in os.listdir(data_path) if f.endswith('.pcd')]
        print(f"✅ Data path exists with {len(pcd_files)} PCD files")
        return True
    else:
        print(f"❌ Data path does not exist: {data_path}")
        print("Please ensure your data is located at the correct path")
        return False

def check_map_file():
    """Check if map file exists"""
    data_dir = "G:/Data/SLAM/test_dynamic/semindoor"
    raw_map_path = os.path.join(data_dir, "raw_map.pcd")
    gt_cloud_path = os.path.join(data_dir, "gt_cloud.pcd")
    
    print(f"\nChecking for map files...")
    
    if os.path.exists(raw_map_path):
        print(f"✅ Found raw_map.pcd")
        return True
    elif os.path.exists(gt_cloud_path):
        print(f"✅ Found gt_cloud.pcd")
        return True
    else:
        print(f"❌ Neither raw_map.pcd nor gt_cloud.pcd found in {data_dir}")
        print("Please ensure you have a map file (raw_map.pcd or gt_cloud.pcd) in your data directory")
        return False

def main():
    """Main setup function"""
    print("BeautyMap Environment Setup")
    print("=" * 40)
    
    success = True
    
    # Check Python version
    if not check_python_version():
        success = False
    
    # Install requirements
    if not install_requirements():
        success = False
    
    # Check data path
    if not check_data_path():
        success = False
    
    # Check map file
    if not check_map_file():
        success = False
    
    print("\n" + "=" * 40)
    if success:
        print("✅ Environment setup completed successfully!")
        print("\nYou can now run the program with:")
        print("python main.py")
        print("\nOr with custom parameters:")
        print("python main.py --data_dir G:/Data/SLAM/test_dynamic/semindoor --dis_range 10 --xy_resolution 0.5 --h_res 0.2")
    else:
        print("❌ Environment setup failed. Please fix the issues above.")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
