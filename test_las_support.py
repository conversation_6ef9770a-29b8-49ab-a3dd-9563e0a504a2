#!/usr/bin/env python3
"""
Test script for LAS file support in BeautyMap
"""

import numpy as np
import os
import sys
import tempfile
import shutil

def test_las_dependencies():
    """Test if LAS dependencies are available"""
    print("Testing LAS dependencies...")
    
    try:
        import laspy
        print("✓ laspy imported successfully")
        print(f"  Version: {laspy.__version__}")
    except ImportError as e:
        print(f"✗ Failed to import laspy: {e}")
        return False
    
    try:
        import pandas
        print("✓ pandas imported successfully")
        print(f"  Version: {pandas.__version__}")
    except ImportError as e:
        print(f"✗ Failed to import pandas: {e}")
        return False
    
    return True


def create_test_las_file(output_path: str, num_points: int = 1000) -> bool:
    """Create a test LAS file for testing"""
    try:
        import laspy
        
        print(f"Creating test LAS file: {output_path}")
        
        # Create random point cloud data
        np.random.seed(42)  # For reproducible results
        
        # Generate points in a 100x100x10 meter area
        x = np.random.uniform(0, 100, num_points)
        y = np.random.uniform(0, 100, num_points)
        z = np.random.uniform(0, 10, num_points)
        
        # Generate intensity values
        intensity = np.random.randint(0, 65535, num_points, dtype=np.uint16)
        
        # Generate GPS time (simulate 10 seconds of data at 100Hz)
        gps_time = np.linspace(0, 10, num_points)
        
        # Create LAS header
        header = laspy.LasHeader(point_format=1, version="1.2")
        
        # Create LAS data
        las_data = laspy.LasData(header)
        las_data.x = x
        las_data.y = y
        las_data.z = z
        las_data.intensity = intensity
        las_data.gps_time = gps_time
        
        # Set required fields
        las_data.classification = np.zeros(num_points, dtype=np.uint8)
        las_data.return_number = np.ones(num_points, dtype=np.uint8)
        las_data.number_of_returns = np.ones(num_points, dtype=np.uint8)
        
        # Write file
        las_data.write(output_path)
        
        print(f"✓ Created test LAS file with {num_points} points")
        return True
        
    except Exception as e:
        print(f"✗ Failed to create test LAS file: {e}")
        return False


def test_las_loading():
    """Test LAS file loading functionality"""
    print("\nTesting LAS file loading...")
    
    # Create temporary test file
    temp_dir = tempfile.mkdtemp()
    test_las_path = os.path.join(temp_dir, "test.las")
    
    try:
        # Create test file
        if not create_test_las_file(test_las_path, 500):
            return False
        
        # Test loading with our utility
        from utils.laspy_utils import load_las_as_pcd_format
        
        point_data, viewpoint = load_las_as_pcd_format(test_las_path)
        
        if point_data is None:
            print("✗ Failed to load LAS file")
            return False
        
        print(f"✓ Loaded LAS file successfully")
        print(f"  Points: {len(point_data)}")
        print(f"  Dimensions: {point_data.shape}")
        print(f"  Viewpoint: {viewpoint}")
        
        # Test BEETree loading
        from lib.bee_tree import BEETree
        
        tree = BEETree()
        tree.set_points_from_file(test_las_path)
        
        print(f"✓ BEETree loaded LAS file successfully")
        print(f"  Original points shape: {tree.original_points.shape}")
        
        return True
        
    except Exception as e:
        print(f"✗ Error testing LAS loading: {e}")
        import traceback
        traceback.print_exc()
        return False
        
    finally:
        # Cleanup
        shutil.rmtree(temp_dir)


def test_las_processor():
    """Test LAS processor functionality"""
    print("\nTesting LAS processor...")
    
    temp_dir = tempfile.mkdtemp()
    test_las_path = os.path.join(temp_dir, "test.las")
    
    try:
        # Create test file with time-based data
        if not create_test_las_file(test_las_path, 2000):
            return False
        
        from utils.laspy_utils import LASProcessor
        
        # Initialize processor
        processor = LASProcessor(test_las_path)
        
        # Load file
        if not processor.load_las_file():
            print("✗ Failed to load LAS file with processor")
            return False
        
        print("✓ LAS processor loaded file successfully")
        
        # Split into frames
        frames = processor.split_by_time(frame_duration=1.0)  # 1 second frames
        
        if not frames:
            print("✗ Failed to split LAS file into frames")
            return False
        
        print(f"✓ Split into {len(frames)} frames")
        print(f"  Average points per frame: {np.mean([len(f) for f in frames]):.1f}")
        
        # Test creating temporary PCD files
        temp_pcd_dir = processor.create_temp_pcd_files()
        
        if not temp_pcd_dir:
            print("✗ Failed to create temporary PCD files")
            return False
        
        print(f"✓ Created temporary PCD files in: {temp_pcd_dir}")
        
        # Check if files were created
        pcd_dir = os.path.join(temp_pcd_dir, "pcd")
        pcd_files = [f for f in os.listdir(pcd_dir) if f.endswith('.pcd')]
        map_file = os.path.join(temp_pcd_dir, "gt_cloud.pcd")
        
        print(f"  Created {len(pcd_files)} PCD files")
        print(f"  Map file exists: {os.path.exists(map_file)}")
        
        # Test saving result
        test_result = processor.point_data[:100]  # Use first 100 points as test result
        output_las_path = os.path.join(temp_dir, "test_output.las")
        
        if processor.save_result_as_las(test_result, output_las_path):
            print(f"✓ Saved result LAS file: {output_las_path}")
            print(f"  Output file size: {os.path.getsize(output_las_path)} bytes")
        else:
            print("✗ Failed to save result LAS file")
            return False
        
        # Cleanup
        processor.cleanup_temp_files()
        print("✓ Cleaned up temporary files")
        
        return True
        
    except Exception as e:
        print(f"✗ Error testing LAS processor: {e}")
        import traceback
        traceback.print_exc()
        return False
        
    finally:
        # Cleanup
        shutil.rmtree(temp_dir)


def main():
    """Run all LAS support tests"""
    print("BeautyMap LAS Support Test")
    print("=" * 50)
    
    success = True
    
    # Test dependencies
    if not test_las_dependencies():
        success = False
        print("\n❌ LAS dependencies test failed")
        print("Please install required packages:")
        print("  pip install laspy pandas")
        return
    
    # Test LAS loading
    if not test_las_loading():
        success = False
    
    # Test LAS processor
    if not test_las_processor():
        success = False
    
    print("\n" + "=" * 50)
    if success:
        print("✅ All LAS support tests passed!")
        print("\nYou can now process LAS files with:")
        print("  python process_las.py input.las")
        print("  python main_las.py las --las_file_path input.las")
    else:
        print("❌ Some LAS support tests failed")
        print("Please check the error messages above")
    
    return success


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
