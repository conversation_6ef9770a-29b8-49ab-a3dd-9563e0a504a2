#!/usr/bin/env python3
"""
Demo script for LAS file processing with BeautyMap
Creates a synthetic LAS file and demonstrates the complete processing pipeline
"""

import numpy as np
import os
import tempfile
import shutil
from main_las import main_las


def create_demo_las_file(output_path: str, duration: float = 5.0, frequency: float = 100.0) -> bool:
    """
    Create a demo LAS file with synthetic moving objects
    
    Args:
        output_path: Path for output LAS file
        duration: Duration of the scan in seconds
        frequency: Point cloud frequency in Hz
        
    Returns:
        bool: True if successful
    """
    try:
        import laspy
        
        print(f"Creating demo LAS file: {output_path}")
        print(f"Duration: {duration}s, Frequency: {frequency}Hz")
        
        # Calculate number of frames and points per frame
        num_frames = int(duration * frequency)
        points_per_frame = 1000
        total_points = num_frames * points_per_frame
        
        print(f"Generating {total_points} points in {num_frames} frames...")
        
        # Initialize arrays
        all_x = []
        all_y = []
        all_z = []
        all_intensity = []
        all_gps_time = []
        
        np.random.seed(42)  # For reproducible results
        
        for frame_idx in range(num_frames):
            current_time = frame_idx / frequency
            
            # Static environment (buildings, trees, etc.)
            static_ratio = 0.8
            num_static = int(points_per_frame * static_ratio)
            
            # Static points - buildings and ground
            static_x = np.random.uniform(-50, 50, num_static)
            static_y = np.random.uniform(-50, 50, num_static)
            static_z = np.random.uniform(0, 20, num_static)
            
            # Add some ground points
            ground_mask = static_z < 2
            static_z[ground_mask] = np.random.uniform(0, 0.5, np.sum(ground_mask))
            
            # Dynamic objects (cars, people, etc.)
            num_dynamic = points_per_frame - num_static
            
            # Moving car 1 - moves along X axis
            car1_center_x = -30 + current_time * 10  # 10 m/s
            car1_center_y = 0
            car1_points = num_dynamic // 3
            
            car1_x = np.random.normal(car1_center_x, 1.5, car1_points)
            car1_y = np.random.normal(car1_center_y, 0.8, car1_points)
            car1_z = np.random.uniform(0.5, 2.0, car1_points)
            
            # Moving car 2 - moves along Y axis
            car2_center_x = 10
            car2_center_y = -30 + current_time * 8  # 8 m/s
            car2_points = num_dynamic // 3
            
            car2_x = np.random.normal(car2_center_x, 0.8, car2_points)
            car2_y = np.random.normal(car2_center_y, 1.5, car2_points)
            car2_z = np.random.uniform(0.5, 2.0, car2_points)
            
            # Walking person - circular motion
            person_radius = 15
            person_angle = current_time * 0.5  # Slow circular motion
            person_center_x = person_radius * np.cos(person_angle)
            person_center_y = person_radius * np.sin(person_angle)
            person_points = num_dynamic - car1_points - car2_points
            
            person_x = np.random.normal(person_center_x, 0.3, person_points)
            person_y = np.random.normal(person_center_y, 0.3, person_points)
            person_z = np.random.uniform(0.5, 1.8, person_points)
            
            # Combine all points for this frame
            frame_x = np.concatenate([static_x, car1_x, car2_x, person_x])
            frame_y = np.concatenate([static_y, car1_y, car2_y, person_y])
            frame_z = np.concatenate([static_z, car1_z, car2_z, person_z])
            
            # Generate intensity (higher for dynamic objects)
            frame_intensity = np.concatenate([
                np.random.randint(100, 200, num_static),      # Static objects
                np.random.randint(200, 300, car1_points),     # Car 1
                np.random.randint(200, 300, car2_points),     # Car 2
                np.random.randint(150, 250, person_points)    # Person
            ])
            
            # GPS time for this frame
            frame_gps_time = np.full(points_per_frame, current_time)
            
            # Add to global arrays
            all_x.extend(frame_x)
            all_y.extend(frame_y)
            all_z.extend(frame_z)
            all_intensity.extend(frame_intensity)
            all_gps_time.extend(frame_gps_time)
        
        # Convert to numpy arrays
        x = np.array(all_x, dtype=np.float64)
        y = np.array(all_y, dtype=np.float64)
        z = np.array(all_z, dtype=np.float64)
        intensity = np.array(all_intensity, dtype=np.uint16)
        gps_time = np.array(all_gps_time, dtype=np.float64)
        
        print(f"Generated {len(x)} total points")
        print(f"Time range: {gps_time.min():.3f} - {gps_time.max():.3f} seconds")
        print(f"Spatial range: X[{x.min():.1f}, {x.max():.1f}], Y[{y.min():.1f}, {y.max():.1f}], Z[{z.min():.1f}, {z.max():.1f}]")
        
        # Create LAS file
        header = laspy.LasHeader(point_format=1, version="1.2")
        las_data = laspy.LasData(header)
        
        # Set point data
        las_data.x = x
        las_data.y = y
        las_data.z = z
        las_data.intensity = intensity
        las_data.gps_time = gps_time
        
        # Set required fields
        las_data.classification = np.zeros(len(x), dtype=np.uint8)
        las_data.return_number = np.ones(len(x), dtype=np.uint8)
        las_data.number_of_returns = np.ones(len(x), dtype=np.uint8)
        
        # Write file
        las_data.write(output_path)
        
        print(f"✅ Demo LAS file created successfully!")
        print(f"File size: {os.path.getsize(output_path) / 1024 / 1024:.1f} MB")
        
        return True
        
    except Exception as e:
        print(f"❌ Error creating demo LAS file: {e}")
        import traceback
        traceback.print_exc()
        return False


def run_demo():
    """Run the complete LAS processing demo"""
    
    print("=" * 70)
    print("BeautyMap LAS Processing Demo")
    print("=" * 70)
    
    # Create temporary directory for demo files
    demo_dir = tempfile.mkdtemp(prefix="beautymap_demo_")
    
    try:
        # Step 1: Create demo LAS file
        print("\n🎯 Step 1: Creating synthetic LAS file with moving objects...")
        demo_las_path = os.path.join(demo_dir, "demo_scan.las")
        
        if not create_demo_las_file(demo_las_path, duration=3.0, frequency=50.0):
            print("❌ Failed to create demo LAS file")
            return
        
        # Step 2: Process with BeautyMap
        print("\n🎯 Step 2: Processing with BeautyMap algorithm...")
        output_las_path = os.path.join(demo_dir, "demo_scan_static.las")
        
        print("Processing parameters:")
        print("  - Frame duration: 0.1 seconds")
        print("  - Processing range: 60 meters")
        print("  - XY resolution: 0.5 meters")
        print("  - Z resolution: 0.2 meters")
        
        main_las(
            las_file_path=demo_las_path,
            output_las_path=output_las_path,
            frame_duration=0.1,
            dis_range=60,
            xy_resolution=0.5,
            h_res=0.2,
            run_frame_num=-1,  # Process all frames
            keep_temp_files=True  # Keep for inspection
        )
        
        # Step 3: Show results
        print("\n🎯 Step 3: Results summary...")
        
        if os.path.exists(output_las_path):
            import laspy
            
            # Load original and processed files
            original = laspy.read(demo_las_path)
            processed = laspy.read(output_las_path)
            
            original_count = len(original.x)
            processed_count = len(processed.x)
            removed_count = original_count - processed_count
            reduction_percent = (removed_count / original_count) * 100
            
            print(f"📊 Processing Results:")
            print(f"  Original points:  {original_count:,}")
            print(f"  Processed points: {processed_count:,}")
            print(f"  Removed points:   {removed_count:,}")
            print(f"  Reduction:        {reduction_percent:.1f}%")
            
            print(f"\n📁 Output files:")
            print(f"  Original LAS:     {demo_las_path}")
            print(f"  Processed LAS:    {output_las_path}")
            print(f"  File sizes:")
            print(f"    Original:  {os.path.getsize(demo_las_path) / 1024 / 1024:.1f} MB")
            print(f"    Processed: {os.path.getsize(output_las_path) / 1024 / 1024:.1f} MB")
            
            print(f"\n✅ Demo completed successfully!")
            print(f"Demo files are saved in: {demo_dir}")
            print(f"You can inspect the results using any LAS viewer software.")
            
        else:
            print("❌ Processing failed - output file not created")
    
    except Exception as e:
        print(f"❌ Demo failed with error: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        # Ask user if they want to keep demo files
        print(f"\n🗂️  Demo files location: {demo_dir}")
        keep_files = input("Keep demo files for inspection? (y/N): ").lower().strip()
        
        if keep_files in ['y', 'yes']:
            print(f"Demo files kept at: {demo_dir}")
        else:
            shutil.rmtree(demo_dir)
            print("Demo files cleaned up.")


if __name__ == "__main__":
    run_demo()
