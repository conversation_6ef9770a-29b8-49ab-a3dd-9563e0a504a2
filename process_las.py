#!/usr/bin/env python3
"""
Simple script to process LAS files with BeautyMap
Usage: python process_las.py input.las [output.las]
"""

import sys
import os
from main_las import main_las


def main():
    """Main entry point for LAS processing"""
    
    if len(sys.argv) < 2:
        print("Usage: python process_las.py input.las [output.las] [options]")
        print("\nOptions:")
        print("  --frame_duration FLOAT    Duration of each frame in seconds (default: 0.1)")
        print("  --dis_range FLOAT         Processing range in meters (default: 10)")
        print("  --xy_resolution FLOAT     XY grid resolution in meters (default: 0.5)")
        print("  --h_res FLOAT             Z grid resolution in meters (default: 0.2)")
        print("  --run_frame_num INT       Number of frames to process, -1 for all (default: -1)")
        print("  --keep_temp_files         Keep temporary PCD files")
        print("\nExamples:")
        print("  python process_las.py input.las")
        print("  python process_las.py input.las output.las")
        print("  python process_las.py input.las --frame_duration 0.05 --dis_range 15")
        return
    
    # Parse arguments
    las_file_path = sys.argv[1]
    
    # Check if input file exists
    if not os.path.exists(las_file_path):
        print(f"Error: Input file not found: {las_file_path}")
        return
    
    # Set output path
    output_las_path = None
    if len(sys.argv) > 2 and not sys.argv[2].startswith('--'):
        output_las_path = sys.argv[2]
    
    # Parse options
    options = {}
    i = 2 if output_las_path is None else 3
    
    while i < len(sys.argv):
        arg = sys.argv[i]
        
        if arg == '--frame_duration' and i + 1 < len(sys.argv):
            options['frame_duration'] = float(sys.argv[i + 1])
            i += 2
        elif arg == '--dis_range' and i + 1 < len(sys.argv):
            options['dis_range'] = float(sys.argv[i + 1])
            i += 2
        elif arg == '--xy_resolution' and i + 1 < len(sys.argv):
            options['xy_resolution'] = float(sys.argv[i + 1])
            i += 2
        elif arg == '--h_res' and i + 1 < len(sys.argv):
            options['h_res'] = float(sys.argv[i + 1])
            i += 2
        elif arg == '--run_frame_num' and i + 1 < len(sys.argv):
            options['run_frame_num'] = int(sys.argv[i + 1])
            i += 2
        elif arg == '--keep_temp_files':
            options['keep_temp_files'] = True
            i += 1
        else:
            print(f"Warning: Unknown option: {arg}")
            i += 1
    
    # Set default output path if not provided
    if output_las_path is None:
        base_name = os.path.splitext(las_file_path)[0]
        output_las_path = f"{base_name}_beautymap.las"
    
    # Process the file
    print("=" * 60)
    print("BeautyMap LAS File Processor")
    print("=" * 60)
    print(f"Input file: {las_file_path}")
    print(f"Output file: {output_las_path}")
    
    if options:
        print("Options:")
        for key, value in options.items():
            print(f"  {key}: {value}")
    
    print("=" * 60)
    
    try:
        main_las(
            las_file_path=las_file_path,
            output_las_path=output_las_path,
            **options
        )
    except Exception as e:
        print(f"Error during processing: {e}")
        import traceback
        traceback.print_exc()


if __name__ == '__main__':
    main()
