#!/usr/bin/env python3
"""
Diagnostic tool for analyzing frame processing issues in BeautyMap
Helps identify why certain frames are skipped with "No query points in map range" error
"""

import numpy as np
import os
import sys
from lib.bee_tree import BEETree
from utils.pcdpy3 import load_pcd
import matplotlib.pyplot as plt


def analyze_frame_issue(data_dir: str, frame_file: str, dis_range: float = 10, 
                       xy_resolution: float = 0.5, h_res: float = 0.2):
    """
    Analyze why a specific frame is being skipped
    
    Args:
        data_dir: Data directory path
        frame_file: Specific frame file to analyze (e.g., "000297.pcd")
        dis_range: Processing range
        xy_resolution: XY resolution
        h_res: Z resolution
    """
    
    print("=" * 70)
    print(f"BeautyMap Frame Issue Diagnostic Tool")
    print("=" * 70)
    print(f"Analyzing frame: {frame_file}")
    print(f"Data directory: {data_dir}")
    print(f"Parameters: dis_range={dis_range}, xy_res={xy_resolution}, h_res={h_res}")
    print("=" * 70)
    
    # Step 1: Load and analyze map
    print("\n🗺️  Step 1: Analyzing map data...")
    
    raw_map_path = os.path.join(data_dir, "raw_map.pcd")
    if not os.path.exists(raw_map_path):
        raw_map_path = os.path.join(data_dir, "gt_cloud.pcd")
    
    if not os.path.exists(raw_map_path):
        print("❌ No map file found!")
        return
    
    # Load map
    Mpts = BEETree()
    Mpts.set_points_from_file(raw_map_path)
    Mpts.set_unit_params(xy_resolution, xy_resolution, h_res)
    Mpts.non_negatification_all_map_points()
    Mpts.calculate_matrix_order()
    
    print(f"✅ Map loaded successfully:")
    print(f"   Points: {len(Mpts.original_points):,}")
    print(f"   Original bounds: X[{Mpts.original_points[:, 0].min():.2f}, {Mpts.original_points[:, 0].max():.2f}]")
    print(f"                    Y[{Mpts.original_points[:, 1].min():.2f}, {Mpts.original_points[:, 1].max():.2f}]")
    print(f"                    Z[{Mpts.original_points[:, 2].min():.2f}, {Mpts.original_points[:, 2].max():.2f}]")
    print(f"   Coordinate offset: {Mpts.coordinate_offset}")
    print(f"   Matrix order: {Mpts.matrix_order}")
    print(f"   Map coverage: {Mpts.matrix_order * xy_resolution:.1f}m x {Mpts.matrix_order * xy_resolution:.1f}m")
    
    # Step 2: Load and analyze problematic frame
    print(f"\n🎯 Step 2: Analyzing frame {frame_file}...")
    
    frame_path = os.path.join(data_dir, "pcd", frame_file)
    if not os.path.exists(frame_path):
        print(f"❌ Frame file not found: {frame_path}")
        return
    
    # Load frame
    Qpts = BEETree()
    Qpts.matrix_order = int(dis_range / xy_resolution)
    Qpts.set_points_from_file(frame_path)
    Qpts.set_unit_params(xy_resolution, xy_resolution, h_res)
    
    print(f"✅ Frame loaded successfully:")
    print(f"   Points: {len(Qpts.original_points):,}")
    print(f"   Original bounds: X[{Qpts.original_points[:, 0].min():.2f}, {Qpts.original_points[:, 0].max():.2f}]")
    print(f"                    Y[{Qpts.original_points[:, 1].min():.2f}, {Qpts.original_points[:, 1].max():.2f}]")
    print(f"                    Z[{Qpts.original_points[:, 2].min():.2f}, {Qpts.original_points[:, 2].max():.2f}]")
    print(f"   Sensor pose: {Qpts.sensor_origin_pose}")
    print(f"   Query matrix order: {Qpts.matrix_order}")
    print(f"   Query coverage: {Qpts.matrix_order * xy_resolution:.1f}m x {Qpts.matrix_order * xy_resolution:.1f}m")
    
    # Step 3: Apply coordinate transformation
    print(f"\n🔄 Step 3: Applying coordinate transformations...")
    
    k = Qpts.transform_on_points(Mpts.coordinate_offset) * xy_resolution / h_res
    Qpts.calculate_query_matrix_start_id()
    
    print(f"✅ Transformations applied:")
    print(f"   Non-negative center: {Qpts.non_negtive_center}")
    print(f"   Start XY: {Qpts.start_xy}")
    print(f"   Start IDs: ({Qpts.start_id_x}, {Qpts.start_id_y})")
    print(f"   K parameter: {k:.4f}")
    
    # Step 4: Analyze range check
    print(f"\n📏 Step 4: Analyzing range check...")
    
    # Reproduce the range check logic
    points_in_map_frame = Qpts.non_negtive_points - [Qpts.start_xy[0], Qpts.start_xy[1], 0]
    
    print(f"   Points after map frame transformation:")
    print(f"     X range: [{points_in_map_frame[:, 0].min():.2f}, {points_in_map_frame[:, 0].max():.2f}]")
    print(f"     Y range: [{points_in_map_frame[:, 1].min():.2f}, {points_in_map_frame[:, 1].max():.2f}]")
    print(f"     Z range: [{points_in_map_frame[:, 2].min():.2f}, {points_in_map_frame[:, 2].max():.2f}]")
    
    # Check range conditions
    x_positive = points_in_map_frame[:, 0] >= 0
    y_positive = points_in_map_frame[:, 1] >= 0
    x_in_range = points_in_map_frame[:, 0] < Qpts.matrix_order * Qpts.unit_x
    y_in_range = points_in_map_frame[:, 1] < Qpts.matrix_order * Qpts.unit_y
    
    print(f"\n   Range check results:")
    print(f"     X >= 0: {np.sum(x_positive):,}/{len(points_in_map_frame):,} points ({np.sum(x_positive)/len(points_in_map_frame)*100:.1f}%)")
    print(f"     Y >= 0: {np.sum(y_positive):,}/{len(points_in_map_frame):,} points ({np.sum(y_positive)/len(points_in_map_frame)*100:.1f}%)")
    print(f"     X < {Qpts.matrix_order * Qpts.unit_x:.1f}: {np.sum(x_in_range):,}/{len(points_in_map_frame):,} points ({np.sum(x_in_range)/len(points_in_map_frame)*100:.1f}%)")
    print(f"     Y < {Qpts.matrix_order * Qpts.unit_y:.1f}: {np.sum(y_in_range):,}/{len(points_in_map_frame):,} points ({np.sum(y_in_range)/len(points_in_map_frame)*100:.1f}%)")
    
    # Combined range check
    points_in_range_id = x_positive & y_positive & x_in_range & y_in_range
    points_in_range = points_in_map_frame[points_in_range_id]
    
    print(f"\n   📊 Final result:")
    print(f"     Points in range: {len(points_in_range):,}/{len(points_in_map_frame):,} ({len(points_in_range)/len(points_in_map_frame)*100:.1f}%)")
    
    if len(points_in_range) == 0:
        print(f"     ❌ NO POINTS IN RANGE - This explains the skip!")
    else:
        print(f"     ✅ Points found in range")
    
    # Step 5: Provide recommendations
    print(f"\n💡 Step 5: Recommendations...")
    
    if len(points_in_range) == 0:
        print("   🔧 Possible solutions:")
        
        # Calculate required range
        max_x_dist = max(abs(points_in_map_frame[:, 0].min()), abs(points_in_map_frame[:, 0].max()))
        max_y_dist = max(abs(points_in_map_frame[:, 1].min()), abs(points_in_map_frame[:, 1].max()))
        required_range = max(max_x_dist, max_y_dist) * 1.2  # Add 20% margin
        
        print(f"   1. Increase dis_range parameter:")
        print(f"      Current: {dis_range}m")
        print(f"      Suggested: {required_range:.1f}m")
        
        # Check if it's a coordinate system issue
        if np.sum(x_positive) < len(points_in_map_frame) * 0.5 or np.sum(y_positive) < len(points_in_map_frame) * 0.5:
            print(f"   2. Coordinate system issue detected:")
            print(f"      Many points have negative coordinates after transformation")
            print(f"      Check if query frames are in the same coordinate system as the map")
        
        # Check sensor position
        sensor_distance = np.sqrt(Qpts.non_negtive_center[0]**2 + Qpts.non_negtive_center[1]**2)
        if sensor_distance > dis_range:
            print(f"   3. Sensor position issue:")
            print(f"      Sensor distance from origin: {sensor_distance:.1f}m")
            print(f"      Processing range: {dis_range}m")
            print(f"      Consider increasing dis_range or check sensor positioning")
    
    else:
        print("   ✅ Frame should process normally with current parameters")
    
    # Step 6: Visualization (optional)
    create_visualization = input("\n📈 Create visualization plots? (y/N): ").lower().strip() == 'y'
    
    if create_visualization:
        create_diagnostic_plots(Mpts, Qpts, points_in_map_frame, points_in_range_id, frame_file)


def create_diagnostic_plots(Mpts, Qpts, points_in_map_frame, points_in_range_id, frame_file):
    """Create diagnostic plots for visualization"""
    
    try:
        fig, axes = plt.subplots(2, 2, figsize=(15, 12))
        fig.suptitle(f'BeautyMap Frame Diagnostic: {frame_file}', fontsize=16)
        
        # Plot 1: Map overview
        ax1 = axes[0, 0]
        map_points = Mpts.original_points[:, :2]
        ax1.scatter(map_points[:, 0], map_points[:, 1], c='blue', alpha=0.5, s=1, label='Map points')
        ax1.set_title('Map Overview (Original Coordinates)')
        ax1.set_xlabel('X (m)')
        ax1.set_ylabel('Y (m)')
        ax1.legend()
        ax1.grid(True, alpha=0.3)
        ax1.axis('equal')
        
        # Plot 2: Query frame overview
        ax2 = axes[0, 1]
        query_points = Qpts.original_points[:, :2]
        ax2.scatter(query_points[:, 0], query_points[:, 1], c='red', alpha=0.7, s=1, label='Query points')
        ax2.scatter(Qpts.sensor_origin_pose[0], Qpts.sensor_origin_pose[1], c='green', s=100, marker='*', label='Sensor')
        ax2.set_title('Query Frame (Original Coordinates)')
        ax2.set_xlabel('X (m)')
        ax2.set_ylabel('Y (m)')
        ax2.legend()
        ax2.grid(True, alpha=0.3)
        ax2.axis('equal')
        
        # Plot 3: Transformed coordinates
        ax3 = axes[1, 0]
        ax3.scatter(points_in_map_frame[:, 0], points_in_map_frame[:, 1], c='red', alpha=0.7, s=1, label='All query points')
        if np.sum(points_in_range_id) > 0:
            points_in_range = points_in_map_frame[points_in_range_id]
            ax3.scatter(points_in_range[:, 0], points_in_range[:, 1], c='green', alpha=0.9, s=2, label='Points in range')
        
        # Draw processing area
        rect_x = [0, Qpts.matrix_order * Qpts.unit_x, Qpts.matrix_order * Qpts.unit_x, 0, 0]
        rect_y = [0, 0, Qpts.matrix_order * Qpts.unit_y, Qpts.matrix_order * Qpts.unit_y, 0]
        ax3.plot(rect_x, rect_y, 'k--', linewidth=2, label='Processing area')
        
        ax3.set_title('Transformed Coordinates (Map Frame)')
        ax3.set_xlabel('X (m)')
        ax3.set_ylabel('Y (m)')
        ax3.legend()
        ax3.grid(True, alpha=0.3)
        ax3.axis('equal')
        
        # Plot 4: Statistics
        ax4 = axes[1, 1]
        ax4.axis('off')
        
        stats_text = f"""
Frame Statistics:
• Total points: {len(Qpts.original_points):,}
• Points in range: {np.sum(points_in_range_id):,}
• Coverage: {np.sum(points_in_range_id)/len(Qpts.original_points)*100:.1f}%

Processing Parameters:
• dis_range: {Qpts.matrix_order * Qpts.unit_x:.1f}m
• xy_resolution: {Qpts.unit_x:.1f}m
• Matrix order: {Qpts.matrix_order}

Coordinate Info:
• Sensor position: ({Qpts.sensor_origin_pose[0]:.1f}, {Qpts.sensor_origin_pose[1]:.1f})
• Query center: ({Qpts.non_negtive_center[0]:.1f}, {Qpts.non_negtive_center[1]:.1f})
• Start XY: ({Qpts.start_xy[0]:.1f}, {Qpts.start_xy[1]:.1f})
        """
        
        ax4.text(0.1, 0.9, stats_text, transform=ax4.transAxes, fontsize=10, 
                verticalalignment='top', fontfamily='monospace')
        
        plt.tight_layout()
        
        # Save plot
        plot_filename = f"diagnostic_{frame_file.replace('.pcd', '')}.png"
        plt.savefig(plot_filename, dpi=150, bbox_inches='tight')
        print(f"   📊 Diagnostic plot saved: {plot_filename}")
        
        # Show plot
        plt.show()
        
    except Exception as e:
        print(f"   ❌ Error creating plots: {e}")


def main():
    """Main function for command line usage"""
    
    if len(sys.argv) < 3:
        print("Usage: python diagnose_frame_issue.py <data_dir> <frame_file> [dis_range] [xy_resolution] [h_res]")
        print("\nExample:")
        print("  python diagnose_frame_issue.py G:/Data/SLAM/test_dynamic/semindoor 000297.pcd")
        print("  python diagnose_frame_issue.py G:/Data/SLAM/test_dynamic/semindoor 000297.pcd 15 0.5 0.2")
        return
    
    data_dir = sys.argv[1]
    frame_file = sys.argv[2]
    dis_range = float(sys.argv[3]) if len(sys.argv) > 3 else 10.0
    xy_resolution = float(sys.argv[4]) if len(sys.argv) > 4 else 0.5
    h_res = float(sys.argv[5]) if len(sys.argv) > 5 else 0.2
    
    analyze_frame_issue(data_dir, frame_file, dis_range, xy_resolution, h_res)


if __name__ == "__main__":
    main()
