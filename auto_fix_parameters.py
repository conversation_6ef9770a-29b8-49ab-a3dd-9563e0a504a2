#!/usr/bin/env python3
"""
Automatic parameter optimization tool for BeautyMap
Analyzes the dataset and suggests optimal parameters to minimize frame skipping
"""

import numpy as np
import os
import sys
from lib.bee_tree import BEETree
from utils.pcdpy3 import load_pcd
from tqdm import tqdm
import json


def analyze_dataset(data_dir: str, sample_frames: int = 50):
    """
    Analyze dataset to determine optimal parameters
    
    Args:
        data_dir: Data directory path
        sample_frames: Number of frames to sample for analysis
        
    Returns:
        dict: Recommended parameters
    """
    
    print("🔍 Analyzing dataset for optimal parameters...")
    
    # Load map
    raw_map_path = os.path.join(data_dir, "raw_map.pcd")
    if not os.path.exists(raw_map_path):
        raw_map_path = os.path.join(data_dir, "gt_cloud.pcd")
    
    if not os.path.exists(raw_map_path):
        print("❌ No map file found!")
        return None
    
    print(f"📍 Loading map from: {raw_map_path}")
    
    # Analyze map
    map_tree = BEETree()
    map_tree.set_points_from_file(raw_map_path)
    
    map_bounds = {
        'x_min': map_tree.original_points[:, 0].min(),
        'x_max': map_tree.original_points[:, 0].max(),
        'y_min': map_tree.original_points[:, 1].min(),
        'y_max': map_tree.original_points[:, 1].max(),
        'z_min': map_tree.original_points[:, 2].min(),
        'z_max': map_tree.original_points[:, 2].max(),
    }
    
    map_size_x = map_bounds['x_max'] - map_bounds['x_min']
    map_size_y = map_bounds['y_max'] - map_bounds['y_min']
    map_size_z = map_bounds['z_max'] - map_bounds['z_min']
    
    print(f"   Map size: {map_size_x:.1f}m x {map_size_y:.1f}m x {map_size_z:.1f}m")
    print(f"   Map points: {len(map_tree.original_points):,}")
    
    # Analyze sample frames
    pcd_dir = os.path.join(data_dir, "pcd")
    if not os.path.exists(pcd_dir):
        print("❌ PCD directory not found!")
        return None
    
    pcd_files = sorted([f for f in os.listdir(pcd_dir) if f.endswith('.pcd')])
    
    if len(pcd_files) == 0:
        print("❌ No PCD files found!")
        return None
    
    # Sample frames for analysis
    if sample_frames > len(pcd_files):
        sample_frames = len(pcd_files)
    
    sample_indices = np.linspace(0, len(pcd_files) - 1, sample_frames, dtype=int)
    sample_files = [pcd_files[i] for i in sample_indices]
    
    print(f"📊 Analyzing {sample_frames} sample frames...")
    
    frame_stats = []
    sensor_positions = []
    point_ranges = []
    
    for pcd_file in tqdm(sample_files, desc="Analyzing frames"):
        try:
            frame_path = os.path.join(pcd_dir, pcd_file)
            
            # Load frame
            frame_tree = BEETree()
            frame_tree.set_points_from_file(frame_path)
            
            # Get sensor position
            sensor_pos = frame_tree.sensor_origin_pose[:3]
            sensor_positions.append(sensor_pos)
            
            # Get point cloud bounds
            frame_bounds = {
                'x_min': frame_tree.original_points[:, 0].min(),
                'x_max': frame_tree.original_points[:, 0].max(),
                'y_min': frame_tree.original_points[:, 1].min(),
                'y_max': frame_tree.original_points[:, 1].max(),
                'z_min': frame_tree.original_points[:, 2].min(),
                'z_max': frame_tree.original_points[:, 2].max(),
            }
            
            # Calculate range from sensor
            distances = np.sqrt(
                (frame_tree.original_points[:, 0] - sensor_pos[0])**2 + 
                (frame_tree.original_points[:, 1] - sensor_pos[1])**2
            )
            
            max_range = distances.max()
            point_ranges.append(max_range)
            
            frame_stats.append({
                'file': pcd_file,
                'points': len(frame_tree.original_points),
                'sensor_pos': sensor_pos,
                'bounds': frame_bounds,
                'max_range': max_range
            })
            
        except Exception as e:
            print(f"   ⚠️  Error analyzing {pcd_file}: {e}")
            continue
    
    if not frame_stats:
        print("❌ No frames could be analyzed!")
        return None
    
    # Calculate statistics
    sensor_positions = np.array(sensor_positions)
    point_ranges = np.array(point_ranges)
    
    # Sensor movement analysis
    sensor_center = sensor_positions.mean(axis=0)
    sensor_distances = np.sqrt(
        (sensor_positions[:, 0] - sensor_center[0])**2 + 
        (sensor_positions[:, 1] - sensor_center[1])**2
    )
    
    max_sensor_distance = sensor_distances.max()
    max_point_range = point_ranges.max()
    avg_point_range = point_ranges.mean()
    
    print(f"\n📈 Dataset Analysis Results:")
    print(f"   Frames analyzed: {len(frame_stats)}")
    print(f"   Sensor movement range: {max_sensor_distance:.1f}m")
    print(f"   Max point range: {max_point_range:.1f}m")
    print(f"   Average point range: {avg_point_range:.1f}m")
    
    # Calculate recommended parameters
    recommendations = calculate_optimal_parameters(
        map_bounds, frame_stats, max_sensor_distance, max_point_range
    )
    
    return recommendations


def calculate_optimal_parameters(map_bounds, frame_stats, max_sensor_distance, max_point_range):
    """Calculate optimal parameters based on analysis"""
    
    map_size_x = map_bounds['x_max'] - map_bounds['x_min']
    map_size_y = map_bounds['y_max'] - map_bounds['y_min']
    map_size_z = map_bounds['z_max'] - map_bounds['z_min']
    
    # Calculate dis_range
    # Should cover: sensor movement + point range + safety margin
    safety_margin = 1.5  # 50% safety margin
    recommended_dis_range = max(
        max_sensor_distance + max_point_range,
        max(map_size_x, map_size_y) * 0.6  # At least 60% of map size
    ) * safety_margin
    
    # Round up to nearest 5m
    recommended_dis_range = np.ceil(recommended_dis_range / 5) * 5
    
    # Calculate xy_resolution
    # Balance between accuracy and performance
    # Aim for 100-500 matrix cells per dimension
    target_matrix_size = 300
    recommended_xy_resolution = recommended_dis_range * 2 / target_matrix_size
    
    # Round to reasonable values
    if recommended_xy_resolution < 0.1:
        recommended_xy_resolution = 0.1
    elif recommended_xy_resolution < 0.5:
        recommended_xy_resolution = 0.2
    elif recommended_xy_resolution < 1.0:
        recommended_xy_resolution = 0.5
    else:
        recommended_xy_resolution = 1.0
    
    # Calculate h_res
    # Should be proportional to xy_resolution but consider Z range
    z_range_factor = map_size_z / max(map_size_x, map_size_y)
    recommended_h_res = recommended_xy_resolution * max(0.2, min(1.0, z_range_factor))
    
    # Round to reasonable values
    if recommended_h_res < 0.1:
        recommended_h_res = 0.1
    elif recommended_h_res < 0.2:
        recommended_h_res = 0.1
    elif recommended_h_res < 0.5:
        recommended_h_res = 0.2
    else:
        recommended_h_res = 0.5
    
    # Calculate expected matrix size
    matrix_order = int(recommended_dis_range * 2 / recommended_xy_resolution)
    
    recommendations = {
        'dis_range': float(recommended_dis_range),
        'xy_resolution': float(recommended_xy_resolution),
        'h_res': float(recommended_h_res),
        'expected_matrix_order': matrix_order,
        'analysis': {
            'map_size': [float(map_size_x), float(map_size_y), float(map_size_z)],
            'max_sensor_distance': float(max_sensor_distance),
            'max_point_range': float(max_point_range),
            'total_frames': len(frame_stats)
        }
    }
    
    return recommendations


def test_parameters(data_dir: str, params: dict, test_frames: int = 10):
    """Test recommended parameters on sample frames"""
    
    print(f"\n🧪 Testing recommended parameters...")
    print(f"   dis_range: {params['dis_range']}")
    print(f"   xy_resolution: {params['xy_resolution']}")
    print(f"   h_res: {params['h_res']}")
    
    # Load map with recommended parameters
    raw_map_path = os.path.join(data_dir, "raw_map.pcd")
    if not os.path.exists(raw_map_path):
        raw_map_path = os.path.join(data_dir, "gt_cloud.pcd")
    
    Mpts = BEETree()
    Mpts.set_points_from_file(raw_map_path)
    Mpts.set_unit_params(params['xy_resolution'], params['xy_resolution'], params['h_res'])
    Mpts.non_negatification_all_map_points()
    Mpts.calculate_matrix_order()
    
    print(f"   Map matrix order: {Mpts.matrix_order}")
    
    # Test on sample frames
    pcd_dir = os.path.join(data_dir, "pcd")
    pcd_files = sorted([f for f in os.listdir(pcd_dir) if f.endswith('.pcd')])
    
    test_indices = np.linspace(0, len(pcd_files) - 1, min(test_frames, len(pcd_files)), dtype=int)
    test_files = [pcd_files[i] for i in test_indices]
    
    success_count = 0
    total_points_in_range = 0
    total_points = 0
    
    for pcd_file in tqdm(test_files, desc="Testing frames"):
        try:
            frame_path = os.path.join(pcd_dir, pcd_file)
            
            # Load and process frame
            Qpts = BEETree()
            Qpts.matrix_order = int(params['dis_range'] / params['xy_resolution'])
            Qpts.set_points_from_file(frame_path)
            Qpts.set_unit_params(params['xy_resolution'], params['xy_resolution'], params['h_res'])
            
            k = Qpts.transform_on_points(Mpts.coordinate_offset) * params['xy_resolution'] / params['h_res']
            Qpts.calculate_query_matrix_start_id()
            
            # Check range
            points_in_map_frame = Qpts.non_negtive_points - [Qpts.start_xy[0], Qpts.start_xy[1], 0]
            points_in_range_id = (
                (points_in_map_frame[:, 0] >= 0) & 
                (points_in_map_frame[:, 1] >= 0) & 
                (points_in_map_frame[:, 0] < Qpts.matrix_order * Qpts.unit_x) & 
                (points_in_map_frame[:, 1] < Qpts.matrix_order * Qpts.unit_y)
            )
            
            points_in_range = np.sum(points_in_range_id)
            total_points_in_range += points_in_range
            total_points += len(points_in_map_frame)
            
            if points_in_range > 0:
                success_count += 1
            
        except Exception as e:
            print(f"   ⚠️  Error testing {pcd_file}: {e}")
            continue
    
    success_rate = success_count / len(test_files) * 100
    coverage_rate = total_points_in_range / total_points * 100 if total_points > 0 else 0
    
    print(f"\n📊 Test Results:")
    print(f"   Success rate: {success_rate:.1f}% ({success_count}/{len(test_files)} frames)")
    print(f"   Point coverage: {coverage_rate:.1f}% ({total_points_in_range:,}/{total_points:,} points)")
    
    return success_rate >= 90  # Consider successful if 90%+ frames process


def main():
    """Main function"""
    
    if len(sys.argv) < 2:
        print("Usage: python auto_fix_parameters.py <data_dir> [sample_frames]")
        print("\nExample:")
        print("  python auto_fix_parameters.py G:/Data/SLAM/test_dynamic/semindoor")
        print("  python auto_fix_parameters.py G:/Data/SLAM/test_dynamic/semindoor 100")
        return
    
    data_dir = sys.argv[1]
    sample_frames = int(sys.argv[2]) if len(sys.argv) > 2 else 50
    
    print("=" * 70)
    print("BeautyMap Automatic Parameter Optimization")
    print("=" * 70)
    
    # Analyze dataset
    recommendations = analyze_dataset(data_dir, sample_frames)
    
    if recommendations is None:
        print("❌ Failed to analyze dataset")
        return
    
    # Display recommendations
    print(f"\n🎯 Recommended Parameters:")
    print(f"   --dis_range {recommendations['dis_range']:.0f}")
    print(f"   --xy_resolution {recommendations['xy_resolution']}")
    print(f"   --h_res {recommendations['h_res']}")
    
    print(f"\n📋 Complete command:")
    print(f"   python main_las.py las --las_file_path input.las \\")
    print(f"       --dis_range {recommendations['dis_range']:.0f} \\")
    print(f"       --xy_resolution {recommendations['xy_resolution']} \\")
    print(f"       --h_res {recommendations['h_res']}")
    
    # Test parameters
    test_success = test_parameters(data_dir, recommendations)
    
    if test_success:
        print(f"\n✅ Parameters validated successfully!")
    else:
        print(f"\n⚠️  Parameters may need further adjustment")
    
    # Save recommendations
    output_file = os.path.join(data_dir, "recommended_parameters.json")
    with open(output_file, 'w') as f:
        json.dump(recommendations, f, indent=2)
    
    print(f"\n💾 Recommendations saved to: {output_file}")


if __name__ == "__main__":
    main()
